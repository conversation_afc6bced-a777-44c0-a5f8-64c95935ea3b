import { useAuth0, User } from '@auth0/auth0-react';
import { useMemo } from 'react';

export interface ExtendedUser extends User {
  role?: string;
  permissions?: string[];
  'https://InnerCircle-resort.com/roles'?: string[];
  'https://InnerCircle-resort.com/permissions'?: string[];
}

export const useAuth0Extended = () => {
  const auth0 = useAuth0();
  const { user, isAuthenticated, isLoading } = auth0;

  const extendedUser = useMemo(() => {
    if (!user || !isAuthenticated) return null;

    const typedUser = user as ExtendedUser;
    
    // Extract roles from custom claims or user metadata
    const roles = typedUser['https://InnerCircle-resort.com/roles'] || 
                  typedUser.role ? [typedUser.role] : 
                  ['super_admin']; // default role

    const permissions = typedUser['https://InnerCircle-resort.com/permissions'] || 
                       typedUser.permissions || 
                       [];

    return {
      ...typedUser,
      role: roles[0] || 'super_admin', // Primary role
      roles,
      permissions,
    };
  }, [user, isAuthenticated]);

  const hasRole = (role: string) => {
    return extendedUser?.roles?.includes(role) || false;
  };

  const hasPermission = (permission: string) => {
    return extendedUser?.permissions?.includes(permission) || false;
  };

  const isSuperAdmin = () => hasRole('super_admin');
  const isResortAdmin = () => hasRole('resort_admin');
  const isViewer = () => hasRole('viewer');
  console.log(extendedUser,
    hasRole,
    hasPermission,
    isSuperAdmin,
    isResortAdmin,
    isViewer,">>>>>>>>>")
  return {
    ...auth0,
    user: extendedUser,
    hasRole,
    hasPermission,
    isSuperAdmin,
    isResortAdmin,
    isViewer,
  };
};
