import { useAuth0 } from "@auth0/auth0-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Building2, Shield, Eye, Lock, AlertCircle, Loader2 } from "lucide-react";

const Auth0Login = () => {
  const { loginWithRedirect, isLoading, error } = useAuth0();

  const handleLogin = () => {
    loginWithRedirect({
      authorizationParams: {
        prompt: 'login',
      },
    });
  };

  const handleSignUp = () => {
    loginWithRedirect({
      authorizationParams: {
        screen_hint: 'signup',
      },
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-cyan-50 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-slate-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="absolute w-screen h-screen top-0 left-0">
        <video
          src="videos/HOME.mp4"
          autoPlay
          muted
          playsInline
          loop
          className="w-full h-full object-cover"
        />
      </div>

      <div className="min-h-screen bg-gradient-to-br z-50 from-slate-50 via-blue-50 to-cyan-50 flex items-center justify-center p-4">
        <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left side - Branding */}
          <div className="hidden z-50 lg:flex flex-col justify-center items-center space-y-8">
            <div className="text-center space-y-4">
              <div className="flex flex-col gap-4 items-center justify-center space-x-3 mb-6">
                <div className="w-40 h-36 p-4 rounded-2xl flex items-center justify-center">
                  <img src="images/vv-logo.png" className="" alt="InnerCircle Logo" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  InnerCircle Resort CMS
                </h1>
              </div>
              <p className="text-xl text-white max-w-md">
                Streamline your resort operations with our comprehensive content management system
              </p>
            </div>
          </div>

          {/* Right side - Login Form */}
          <div className="flex items-center justify-center z-50">
            <Card className="w-full max-w-md bg-white/90 backdrop-blur-md border-slate-200 shadow-xl">
              <CardHeader className="text-center pb-6">
                <div className="flex items-center justify-center space-x-2 mb-4 lg:hidden">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg flex items-center justify-center">
                    <Building2 className="w-6 h-6 text-white" />
                  </div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                    InnerCircle Resort CMS
                  </h1>
                </div>
                <CardTitle className="text-2xl font-bold">Welcome to InnerCircle</CardTitle>
                <CardDescription className="text-base">
                  Sign in with your Auth0 account to access your dashboard
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="w-4 h-4 text-red-600" />
                      <p className="text-sm text-red-700">
                        Authentication error: {error.message}
                      </p>
                    </div>
                  </div>
                )}

                <div className="space-y-4">
                  <Button 
                    onClick={handleLogin}
                    className="w-full h-12 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold text-base"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <div className="flex items-center space-x-2">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span>Signing In...</span>
                      </div>
                    ) : (
                      <>
                        <Lock className="w-4 h-4 mr-2" />
                        Sign In with Auth0
                      </>
                    )}
                  </Button>

                  <Button 
                    onClick={handleSignUp}
                    variant="outline"
                    className="w-full h-12 border-2 border-blue-200 hover:bg-blue-50 text-blue-700 font-semibold text-base"
                    disabled={isLoading}
                  >
                    Create New Account
                  </Button>
                </div>

                <div className="pt-6 border-t border-slate-200">
                  <p className="text-sm text-slate-600 mb-4 font-medium">Available Roles:</p>
                  <div className="space-y-3">
                    {[
                      { label: "Super Admin", color: "bg-red-100 text-red-700 border-red-200", icon: Shield },
                      { label: "Resort Admin", color: "bg-blue-100 text-blue-700 border-blue-200", icon: Building2 },
                      { label: "Sales Representative", color: "bg-gray-100 text-gray-700 border-gray-200", icon: Eye }
                    ].map((role) => {
                      const Icon = role.icon;
                      return (
                        <div 
                          key={role.label} 
                          className="flex items-center justify-between p-3 bg-slate-50 rounded-lg"
                        >
                          <span className="text-sm font-medium text-slate-700">
                            {role.label}
                          </span>
                          <Badge className={`${role.color} text-xs font-medium`}>
                            <Icon className="w-3 h-3 mr-1" />
                            {role.label}
                          </Badge>
                        </div>
                      );
                    })}
                  </div>
                  
                  <div className="mt-4 flex items-start space-x-2 p-3 bg-blue-50 rounded-lg">
                    <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-blue-700">
                      Roles are assigned by your administrator and will be available after successful authentication
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Auth0Login;
