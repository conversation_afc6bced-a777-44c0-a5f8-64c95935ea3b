# Auth0 Integration Setup Guide

This guide will help you set up Auth0 authentication for the InnerCircle Resort CMS application.

## Prerequisites

1. An Auth0 account (sign up at [auth0.com](https://auth0.com))
2. Node.js and npm installed
3. The InnerCircle Resort CMS application

## Step 1: Create Auth0 Application

1. Log in to your Auth0 Dashboard
2. Go to **Applications** > **Applications**
3. Click **Create Application**
4. Choose **Single Page Web Applications**
5. Select **React** as the technology
6. Click **Create**

## Step 2: Configure Auth0 Application Settings

In your Auth0 application settings, configure the following:

### Allowed Callback URLs
```
http://localhost:5173,
https://your-production-domain.com
```

### Allowed Logout URLs
```
http://localhost:5173,
https://your-production-domain.com
```

### Allowed Web Origins
```
http://localhost:5173,
https://your-production-domain.com
```

### Allowed Origins (CORS)
```
http://localhost:5173,
https://your-production-domain.com
```

## Step 3: Set Up Environment Variables

1. Copy the `.env.example` file to `.env`:
```bash
cp .env.example .env
```

2. Fill in your Auth0 configuration in the `.env` file:
```env
VITE_AUTH0_DOMAIN=your-auth0-domain.auth0.com
VITE_AUTH0_CLIENT_ID=your-auth0-client-id
VITE_AUTH0_AUDIENCE=your-auth0-api-identifier
VITE_AUTH0_REDIRECT_URI=http://localhost:5173
VITE_AUTH0_LOGOUT_URI=http://localhost:5173
```

### Where to find these values:

- **VITE_AUTH0_DOMAIN**: Found in your Auth0 application settings
- **VITE_AUTH0_CLIENT_ID**: Found in your Auth0 application settings
- **VITE_AUTH0_AUDIENCE**: Your Auth0 API identifier (optional, for API access)
- **VITE_AUTH0_REDIRECT_URI**: Where users are redirected after login
- **VITE_AUTH0_LOGOUT_URI**: Where users are redirected after logout

## Step 4: Configure User Roles and Permissions

### Option 1: Using Auth0 Rules (Legacy)

Create a rule in Auth0 Dashboard > Auth Pipeline > Rules:

```javascript
function addRolesToUser(user, context, callback) {
  const assignedRoles = (context.authorization || {}).roles || [];
  
  // Default role assignment logic
  let userRole = 'viewer';
  
  if (user.email && user.email.includes('admin@')) {
    userRole = 'super_admin';
  } else if (user.email && user.email.includes('resort@')) {
    userRole = 'resort_admin';
  }
  
  // Add roles to the user's token
  const namespace = 'https://InnerCircle-resort.com/';
  context.idToken[namespace + 'roles'] = assignedRoles.length > 0 ? assignedRoles : [userRole];
  context.accessToken[namespace + 'roles'] = assignedRoles.length > 0 ? assignedRoles : [userRole];
  
  callback(null, user, context);
}
```

### Option 2: Using Auth0 Actions (Recommended)

1. Go to Auth0 Dashboard > Actions > Flows
2. Select **Login**
3. Create a new action with this code:

```javascript
exports.onExecutePostLogin = async (event, api) => {
  const assignedRoles = event.authorization?.roles || [];
  
  // Default role assignment logic
  let userRole = 'viewer';
  
  if (event.user.email && event.user.email.includes('admin@')) {
    userRole = 'super_admin';
  } else if (event.user.email && event.user.email.includes('resort@')) {
    userRole = 'resort_admin';
  }
  
  // Add roles to the user's token
  const namespace = 'https://InnerCircle-resort.com/';
  api.idToken.setCustomClaim(namespace + 'roles', assignedRoles.length > 0 ? assignedRoles : [userRole]);
  api.accessToken.setCustomClaim(namespace + 'roles', assignedRoles.length > 0 ? assignedRoles : [userRole]);
};
```

## Step 5: Test the Integration

1. Start your development server:
```bash
npm run dev
```

2. Navigate to `http://localhost:5173`
3. You should see the Auth0 login page
4. Try logging in with different email patterns to test role assignment:
   - `<EMAIL>` → Super Admin
   - `<EMAIL>` → Resort Admin
   - `<EMAIL>` → Viewer

## Available Roles

The application supports three roles:

- **super_admin**: Full access to all features including resort management and system settings
- **resort_admin**: Access to resort operations, analytics, and team management
- **viewer**: Basic access to referrals, rewards, and contact management

## Role-Based Access Control

The application uses role-based access control for different features:

- **Dashboard**: Available to all authenticated users
- **Sales Team Management**: Admin only (super_admin, resort_admin)
- **Analytics**: Admin only (super_admin, resort_admin)
- **Notifications**: Admin only (super_admin, resort_admin)
- **Resort Management**: Super Admin only (super_admin)
- **System Settings**: Super Admin only (super_admin)
- **Profile**: Available to all authenticated users

## Troubleshooting

### Common Issues

1. **"Invalid audience" error**: Make sure your VITE_AUTH0_AUDIENCE is correctly set
2. **"Invalid redirect URI" error**: Ensure your callback URLs are properly configured in Auth0
3. **Role not showing**: Check that your Auth0 rule/action is properly adding custom claims
4. **CORS errors**: Verify your allowed origins are configured in Auth0

### Debug Mode

To enable debug mode, add this to your `.env`:
```env
VITE_AUTH0_DEBUG=true
```

## Production Deployment

1. Update your Auth0 application settings with production URLs
2. Set production environment variables
3. Ensure HTTPS is enabled for production domains
4. Test the authentication flow in production

## Security Considerations

1. Never commit your `.env` file to version control
2. Use different Auth0 applications for development and production
3. Regularly rotate your Auth0 client secrets
4. Monitor Auth0 logs for suspicious activity
5. Implement proper session management and token refresh

## Support

For Auth0-specific issues, refer to the [Auth0 Documentation](https://auth0.com/docs).
For application-specific issues, contact your development team.
