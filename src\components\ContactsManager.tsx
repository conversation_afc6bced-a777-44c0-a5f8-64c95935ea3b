
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Users,
  Search,
  Filter,
  Download,
  Upload,
  Phone,
  Mail,
  Calendar,
  MapPin,
  Zap,
  Smartphone,
  Globe,
  Loader2,
  RefreshCw
} from "lucide-react";
import { toast } from "sonner";

// Types for API response
interface CustomField {
  id: string;
  value: string;
}

interface Contact {
  id: string;
  locationId: string;
  contactName: string;
  firstName: string;
  lastName: string | null;
  firstNameRaw: string;
  lastNameRaw: string | null;
  companyName: string | null;
  email: string;
  phone: string | null;
  dnd: boolean;
  dndSettings: Record<string, any>;
  type: string;
  source: string | null;
  assignedTo: string | null;
  city: string | null;
  state: string | null;
  postalCode: string | null;
  address1: string | null;
  dateAdded: string;
  dateUpdated: string;
  dateOfBirth: string | null;
  businessId: string | null;
  tags: string[];
  followers: any[];
  country: string;
  website: string | null;
  timezone: string | null;
  additionalEmails: string[];
  customFields: CustomField[];
}

interface ContactsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: {
    contacts: Contact[];
    totalCount: number;
    meta: {
      total: number;
      nextPageUrl: string | null;
      startAfterId: string;
      startAfter: number;
      currentPage: number;
      nextPage: number | null;
      prevPage: number | null;
    };
  };
  timestamp: string;
}

const ContactsManager = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [nextPageUrl, setNextPageUrl] = useState<string | null>(null);

  // API configuration
  const API_BASE_URL = "https://dev-api.innercircle.onpointsoft.com";
  const LOCATION_ID = "84ntE6WXgVDwny78VpzO";

  // Fetch contacts from API
  const fetchContacts = async (pageUrl?: string) => {
    setLoading(true);
    try {
      const url = pageUrl || `${API_BASE_URL}/oauth/locations/${LOCATION_ID}/contacts`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ContactsResponse = await response.json();

      if (data.success) {
        if (pageUrl) {
          // If loading next page, append to existing contacts
          setContacts(prev => [...prev, ...data.data.contacts]);
        } else {
          // If loading first page, replace contacts
          setContacts(data.data.contacts);
        }
        setTotalCount(data.data.totalCount);
        setNextPageUrl(data.data.meta.nextPageUrl);
        setCurrentPage(data.data.meta.currentPage);
        toast.success(`Loaded ${data.data.contacts.length} contacts successfully!`);
      } else {
        throw new Error(data.message || 'Failed to fetch contacts');
      }
    } catch (error) {
      console.error('Error fetching contacts:', error);
      toast.error('Failed to fetch contacts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Load contacts on component mount
  useEffect(() => {
    fetchContacts();
  }, []);

  const handleRefreshContacts = () => {
    setContacts([]);
    setCurrentPage(1);
    setNextPageUrl(null);
    fetchContacts();
  };

  const handleLoadMore = () => {
    if (nextPageUrl && !loading) {
      fetchContacts(nextPageUrl);
    }
  };

  const handleExportContacts = () => {
    toast.success("Contacts exported successfully!");
  };

  const handleViewContact = (contact: Contact) => {
    setSelectedContact(contact);
    setIsDetailOpen(true);
  };

  const getStatusBadge = (type: string) => {
    const colors = {
      lead: "bg-blue-100 text-blue-700 border-blue-200",
      contact: "bg-green-100 text-green-700 border-green-200",
      prospect: "bg-yellow-100 text-yellow-700 border-yellow-200",
      customer: "bg-purple-100 text-purple-700 border-purple-200"
    };
    return colors[type.toLowerCase() as keyof typeof colors] || colors.lead;
  };

  const getLocationString = (contact: Contact) => {
    const parts = [contact.city, contact.state, contact.country].filter(Boolean);
    return parts.length > 0 ? parts.join(", ") : "N/A";
  };

  const filteredContacts = contacts.filter(contact => {
    const matchesSearch =
      contact.contactName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (contact.phone && contact.phone.includes(searchTerm)) ||
      contact.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === "all" || contact.type.toLowerCase() === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-slate-800">Contact Management</h2>
          <p className="text-slate-600 mt-1">Manage synced contacts and CRM integrations</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefreshContacts}
            disabled={loading}
          >
            {loading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4 mr-2" />
            )}
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExportContacts}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Total Contacts</p>
                <p className="text-2xl font-bold text-slate-800">
                  {loading ? (
                    <Loader2 className="w-6 h-6 animate-spin" />
                  ) : (
                    totalCount.toLocaleString()
                  )}
                </p>
              </div>
              <Users className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Leads</p>
                <p className="text-2xl font-bold text-slate-800">
                  {contacts.filter(c => c.type === 'lead').length}
                </p>
              </div>
              <Phone className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">With Phone</p>
                <p className="text-2xl font-bold text-slate-800">
                  {contacts.filter(c => c.phone && c.phone.trim() !== '').length}
                </p>
              </div>
              <Mail className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Loaded</p>
                <p className="text-2xl font-bold text-slate-800">{contacts.length}</p>
              </div>
              <Smartphone className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[300px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input
                  placeholder="Search by name, email, or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="lead">Lead</SelectItem>
                <SelectItem value="contact">Contact</SelectItem>
                <SelectItem value="prospect">Prospect</SelectItem>
                <SelectItem value="customer">Customer</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Contacts Table */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardHeader>
          <CardTitle>Synced Contacts</CardTitle>
          <CardDescription>
            Contacts imported from CRM - Showing {contacts.length} of {totalCount} total contacts
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading && contacts.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-8 h-8 animate-spin mr-2" />
              <span>Loading contacts...</span>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Contact Info</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Tags</TableHead>
                    <TableHead>Date Added</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredContacts.map((contact) => (
                    <TableRow key={contact.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium text-slate-800">{contact.contactName}</div>
                          <div className="text-sm text-slate-600">{contact.email}</div>
                          <div className="text-sm text-slate-500">{contact.phone || 'No phone'}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusBadge(contact.type)}>
                          {contact.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-slate-600">
                          <MapPin className="w-3 h-3 mr-1" />
                          {getLocationString(contact)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {contact.tags.slice(0, 2).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {contact.tags.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{contact.tags.length - 2}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-slate-600">
                          {new Date(contact.dateAdded).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewContact(contact)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Load More Button */}
              {nextPageUrl && (
                <div className="flex justify-center mt-6">
                  <Button
                    onClick={handleLoadMore}
                    disabled={loading}
                    variant="outline"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Loading...
                      </>
                    ) : (
                      'Load More Contacts'
                    )}
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Contact Detail Dialog */}
      <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Contact Details</DialogTitle>
            <DialogDescription>
              Detailed information about {selectedContact?.contactName}
            </DialogDescription>
          </DialogHeader>

          {selectedContact && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-slate-600">Full Name</Label>
                  <p className="text-slate-800 font-medium">{selectedContact.contactName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-slate-600">Type</Label>
                  <Badge className={getStatusBadge(selectedContact.type)}>
                    {selectedContact.type}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-slate-600">Email</Label>
                  <p className="text-slate-800">{selectedContact.email}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-slate-600">Phone</Label>
                  <p className="text-slate-800">{selectedContact.phone || 'Not provided'}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-slate-600">First Name</Label>
                  <p className="text-slate-800">{selectedContact.firstNameRaw || 'N/A'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-slate-600">Last Name</Label>
                  <p className="text-slate-800">{selectedContact.lastNameRaw || 'N/A'}</p>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label className="text-sm font-medium text-slate-600">City</Label>
                  <p className="text-slate-800">{selectedContact.city || 'N/A'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-slate-600">State</Label>
                  <p className="text-slate-800">{selectedContact.state || 'N/A'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-slate-600">Country</Label>
                  <p className="text-slate-800">{selectedContact.country}</p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-slate-600">Tags</Label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {selectedContact.tags.length > 0 ? (
                    selectedContact.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-slate-500 text-sm">No tags</span>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-slate-600">Date Added</Label>
                  <p className="text-slate-800">
                    {new Date(selectedContact.dateAdded).toLocaleString()}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-slate-600">Last Updated</Label>
                  <p className="text-slate-800">
                    {new Date(selectedContact.dateUpdated).toLocaleString()}
                  </p>
                </div>
              </div>

              {selectedContact.customFields.length > 0 && (
                <div>
                  <Label className="text-sm font-medium text-slate-600">Custom Fields</Label>
                  <div className="mt-2 space-y-2">
                    {selectedContact.customFields.map((field, index) => (
                      <div key={index} className="flex justify-between items-center p-2 bg-slate-50 rounded">
                        <span className="text-sm text-slate-600">Field {field.id}</span>
                        <span className="text-sm font-medium">{field.value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ContactsManager;
